// --- DOM Elements ---
let elements = {};

// --- App State & Constants ---
let apiKey = '';
let apiUrl = '';
let allChats = [];
let currentChatId = null;
let selectedImages = []; // 存储当前选择的图片
const MODEL_NAME = 'gemini-2.5-flash';
const DEFAULT_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${MODEL_NAME}:generateContent`;

// 各提供商的默认API地址
const PROVIDER_API_URLS = {
    'DeepSeek': 'https://api.deepseek.com/v1/chat/completions',
    'Gemini': `https://generativelanguage.googleapis.com/v1beta/models/${MODEL_NAME}:generateContent`,
    'OpenRouter': 'https://openrouter.ai/api/v1/chat/completions',
    'Custom': '' // 自定义模式下用户需要手动输入
};
const STORAGE_KEY = 'ai_multi_chat_pro_data';
const API_KEY_STORAGE_KEY = 'ai_multi_chat_pro_api_key';
const API_URL_STORAGE_KEY = 'ai_multi_chat_pro_api_url';

const ICONS = {
    newChat: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 4a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2h-6v6a1 1 0 1 1-2 0v-6H5a1 1 0 1 1 0-2h6V5a1 1 0 0 1 1-1z"/></svg><span>新建对话</span>`,
    delete: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M17 6h5v2h-2v13a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V8H2V6h5V3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v3zm-2-2H9v2h6V4z"/></svg>`,
    send: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z"/></svg>`,
    aiAvatar: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C15.31 2 18 4.69 18 8V9C19.1 9 20 9.9 20 11V16C20 17.1 19.1 18 18 18H17V20C17 21.1 16.1 22 15 22H9C7.9 22 7 21.1 7 20V18H6C4.9 18 4 17.1 4 16V11C4 9.9 4.9 9 6 9V8C6 4.69 8.69 2 12 2ZM12 4C9.79 4 8 5.79 8 8V9H16V8C16 5.79 14.21 4 12 4ZM9 11V13H11V11H9ZM13 11V13H15V11H13Z"/></svg>`,
    welcomeLogo: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12.0006 22.0001C6.47775 22.0001 2.00061 17.5229 2.00061 12.0001C2.00061 6.47721 6.47775 2.00008 12.0006 2.00008C17.5234 2.00008 22.0006 6.47721 22.0006 12.0001C22.0006 17.5229 17.5234 22.0001 12.0006 22.0001ZM11.0006 14.0001H13.0006V16.0001H11.0006V14.0001ZM11.0006 8.00008H13.0006V12.0001H11.0006V8.00008Z"></path></svg>`,
    settings: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/></svg>`,
    close: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>`,
    warning: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>`
};

// --- Event Listeners ---
document.addEventListener('DOMContentLoaded', () => {
    // 获取DOM元素
    elements = {
        appContainer: document.getElementById('app-container'),
        apiKeyView: document.getElementById('api-key-view'),
        apiKeyInput: document.getElementById('api-key-input'),
        submitApiKeyButton: document.getElementById('submit-api-key-button'),
        chatViewContainer: document.getElementById('chat-view-container'),
        newChatButton: document.getElementById('new-chat-button'),
        chatList: document.getElementById('chat-list'),
        chatTitle: document.getElementById('chat-title'),
        chatbox: document.getElementById('chatbox'),
        welcomeScreen: document.getElementById('welcome-screen'),
        userInput: document.getElementById('user-input'),
        sendButton: document.getElementById('send-button'),
        imageUploadButton: document.getElementById('image-upload-button'),
        imageInput: document.getElementById('image-input'),
        imagePreviewContainer: document.getElementById('image-preview-container'),
        imagePreviewList: document.getElementById('image-preview-list'),
        clearImagesButton: document.getElementById('clear-images-button'),
        sidebar: document.getElementById('sidebar'),
        mobileMenuButton: document.getElementById('mobile-menu-button'),
        mobileOverlay: document.getElementById('mobile-overlay'),
        settingsButton: document.getElementById('settings-button'),
        settingsModal: document.getElementById('settings-modal'),
        settingsOverlay: document.getElementById('settings-overlay'),
        closeSettingsButton: document.getElementById('close-settings-button'),
        settingsApiKeyInput: document.getElementById('settings-api-key-input'),
        settingsApiUrlInput: document.getElementById('settings-api-url-input'),
        saveSettingsButton: document.getElementById('save-settings-button'),
        resetSettingsButton: document.getElementById('reset-settings-button'),
        clearAllDataButton: document.getElementById('clear-all-data-button'),
        // 新的API Key界面元素
        toggleApiKeyVisibilityBtn: document.getElementById('toggle-api-key-visibility'),
        eyeOpenIcon: document.getElementById('eye-open-icon'),
        eyeClosedIcon: document.getElementById('eye-closed-icon'),
        modelProviderRadios: document.querySelectorAll('input[name="model_provider"]'),
        apiKeyHelpLink: document.getElementById('api-key-help-link'),
        testConnectionBtn: document.getElementById('test-connection-btn'),
        apiKeyWarning: document.getElementById('api-key-warning'),
        customApiUrlGroup: document.getElementById('custom-api-url-group'),
        customApiUrlInput: document.getElementById('custom-api-url-input'),
        // 设置模态框中的新元素
        customProviderSelect: document.getElementById('custom-provider-select'),
        selectTrigger: document.getElementById('select-trigger'),
        selectValue: document.getElementById('select-value'),
        selectOptions: document.getElementById('select-options'),
        settingsProviderLink: document.getElementById('settings-provider-link'),
        settingsToggleApiKeyVisibilityBtn: document.getElementById('settings-toggle-api-key-visibility'),
        settingsEyeOpenIcon: document.getElementById('settings-eye-open-icon'),
        settingsEyeClosedIcon: document.getElementById('settings-eye-closed-icon'),
        apiUrlHint: document.getElementById('api-url-hint'),
        apiUrlGroup: document.getElementById('api-url-group')
    };

    // 设置图标
    elements.newChatButton.innerHTML = ICONS.newChat;
    elements.sendButton.innerHTML = ICONS.send;
    document.querySelector('.welcome-logo').innerHTML = ICONS.welcomeLogo;

    // 自动填充已保存的API key和URL
    loadApiKeyFromStorage();
    loadApiUrlFromStorage();

    // 初始化API Key界面
    initializeApiKeyInterface();

    // 设置事件监听器
    setupEventListeners();

    // 初始化新的API Key界面
    initializeApiKeyInterface();
});
function setupEventListeners() {
    // 基本事件监听器
    elements.submitApiKeyButton.addEventListener('click', initializeApp);
    elements.apiKeyInput.addEventListener('keydown', (e) => { if (e.key === 'Enter') initializeApp(); });
    elements.newChatButton.addEventListener('click', createNewChat);
    elements.sendButton.addEventListener('click', sendMessage);
    elements.userInput.addEventListener('keydown', (e) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); sendMessage(); } });
    elements.userInput.addEventListener('input', () => { elements.userInput.style.height = 'auto'; elements.userInput.style.height = (elements.userInput.scrollHeight) + 'px'; });

    // 图片上传相关事件监听
    elements.imageUploadButton.addEventListener('click', () => elements.imageInput.click());
    elements.imageInput.addEventListener('change', handleImageUpload);
    elements.clearImagesButton.addEventListener('click', clearAllImages);

    // 移动端菜单事件监听
    elements.mobileMenuButton.addEventListener('click', toggleMobileSidebar);
    elements.mobileOverlay.addEventListener('click', closeMobileSidebar);

    // 设置相关事件监听
    elements.settingsButton.addEventListener('click', openSettings);
    elements.closeSettingsButton.addEventListener('click', closeSettings);
    elements.settingsOverlay.addEventListener('click', (e) => {
        if (e.target === elements.settingsOverlay) closeSettings();
    });
    elements.saveSettingsButton.addEventListener('click', saveSettings);
    elements.resetSettingsButton.addEventListener('click', resetSettings);
    elements.clearAllDataButton.addEventListener('click', clearAllData);

    // 设置输入框Enter键保存
    elements.settingsApiKeyInput.addEventListener('keydown', (e) => { if (e.key === 'Enter') saveSettings(); });
    elements.settingsApiUrlInput.addEventListener('keydown', (e) => { if (e.key === 'Enter') saveSettings(); });

    // 设置模态框中的API Key可见性切换
    if (elements.settingsToggleApiKeyVisibilityBtn) {
        elements.settingsToggleApiKeyVisibilityBtn.addEventListener('click', toggleSettingsApiKeyVisibility);
    }

    // 设置模态框中的提供商选择
    if (elements.settingsProviderSelect) {
        elements.settingsProviderSelect.addEventListener('change', onSettingsProviderChange);
    }

    // 设置模态框中的提供商选择
    if (elements.settingsProviderSelect) {
        elements.settingsProviderSelect.addEventListener('change', onSettingsProviderChange);
    }

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (elements.settingsOverlay.classList.contains('active')) {
                closeSettings();
            }
        }
    });

    // 新的API Key界面事件监听器
    if (elements.toggleApiKeyVisibilityBtn) {
        elements.toggleApiKeyVisibilityBtn.addEventListener('click', toggleApiKeyVisibility);
    }

    if (elements.modelProviderRadios) {
        elements.modelProviderRadios.forEach(radio => {
            radio.addEventListener('change', updateApiKeySection);
        });
    }

    if (elements.testConnectionBtn) {
        elements.testConnectionBtn.addEventListener('click', testConnection);
    }

    // 自定义API地址输入框事件监听器
    if (elements.customApiUrlInput) {
        elements.customApiUrlInput.addEventListener('input', updateStartAppButtonState);
    }

    // API Key输入变化监听
    elements.apiKeyInput.addEventListener('input', updateStartAppButtonState);

    // 自定义选择器事件监听器
    if (elements.selectTrigger) {
        elements.selectTrigger.addEventListener('click', toggleCustomSelect);
    }

    // 点击选项时的处理
    if (elements.selectOptions) {
        elements.selectOptions.addEventListener('click', handleSelectOption);
    }

    // 点击外部关闭选择器
    document.addEventListener('click', function(e) {
        if (!elements.customProviderSelect?.contains(e.target)) {
            closeCustomSelect();
        }
    });
}

document.querySelectorAll('.prompt-example').forEach(p => {
    p.addEventListener('click', () => {
        elements.userInput.value = p.textContent;
        sendMessage();
    });
});

// --- Initialization ---
function initializeApp() {
    const key = elements.apiKeyInput.value.trim();

    // 验证API Key
    if (!key) {
        showToast('请先输入有效的API Key。', 'error', 3000);
        return;
    }

    // 自定义模式下验证API地址
    if (selectedProvider === 'Custom') {
        const customApiUrl = elements.customApiUrlInput ? elements.customApiUrlInput.value.trim() : '';
        if (!customApiUrl) {
            showToast('自定义模式下必须输入API地址。', 'error', 3000);
            elements.customApiUrlInput?.focus();
            return;
        }
        // 保存自定义API地址
        apiUrl = customApiUrl;
        saveApiUrlToStorage(customApiUrl);
    }

    // 保存API Key
    apiKey = key;
    saveApiKey(); // 使用新的保存函数
    saveApiKeyToStorage(key); // 保持向后兼容

    showToast(`${getProviderDisplayName(selectedProvider)} 设置已保存，正在进入应用...`, 'success');

    setTimeout(() => {
        elements.apiKeyView.style.display = 'none';
        elements.appContainer.style.display = 'flex';
        loadDataFromStorage();
        renderSidebar();
        renderChatContent();
        elements.userInput.focus();
    }, 1000);
}

// --- API Key & URL Storage Functions ---
function saveApiKeyToStorage(key) {
    try {
        localStorage.setItem(API_KEY_STORAGE_KEY, key);
    } catch (e) {
        console.error("保存API Key失败:", e);
    }
}

function loadApiKeyFromStorage() {
    try {
        const savedApiKey = localStorage.getItem(API_KEY_STORAGE_KEY);
        if (savedApiKey) {
            elements.apiKeyInput.value = savedApiKey;
            apiKey = savedApiKey;
        }
    } catch (e) {
        console.error("加载API Key失败:", e);
    }
}

function saveApiUrlToStorage(url) {
    try {
        localStorage.setItem(API_URL_STORAGE_KEY, url);
    } catch (e) {
        console.error("保存API URL失败:", e);
    }
}

function loadApiUrlFromStorage() {
    try {
        const savedApiUrl = localStorage.getItem(API_URL_STORAGE_KEY);
        apiUrl = savedApiUrl || DEFAULT_API_URL;
        return apiUrl;
    } catch (e) {
        console.error("加载API URL失败:", e);
        apiUrl = DEFAULT_API_URL;
        return apiUrl;
    }
}

function getApiUrl() {
    const baseUrl = apiUrl || DEFAULT_API_URL;
    return `${baseUrl}?key=${apiKey}`;
}

// --- Data Management ---
function loadDataFromStorage() {
    try {
        const data = JSON.parse(localStorage.getItem(STORAGE_KEY));
        if (data && data.chats && data.chats.length > 0 && data.currentChatId) {
            allChats = data.chats;
            currentChatId = data.currentChatId;
            if (!allChats.find(c => c.id === currentChatId)) {
                currentChatId = allChats[0]?.id || null;
            }
        }
    } catch (e) { console.error("解析本地数据失败:", e); }

    if (!allChats.length || !currentChatId) { createNewChat(false); }
}

function saveDataToStorage() { localStorage.setItem(STORAGE_KEY, JSON.stringify({ chats: allChats, currentChatId: currentChatId })); }

function createNewChat(switchImmediately = true) {
    const newId = Date.now().toString();
    const newChat = { id: newId, title: "新对话", history: [] };
    allChats.unshift(newChat);
    if (switchImmediately) { switchChat(newId); } 
    else { currentChatId = newId; }
    saveDataToStorage();
    renderSidebar();
}

function deleteChat(chatId) {
    if (allChats.length <= 1) {
        alert("这是最后一个对话，无法删除。");
        return;
    }

    const chatToDelete = allChats.find(c => c.id === chatId);
    if (!chatToDelete) return;

    if (confirm(`确定要删除对话 "${chatToDelete.title}" 吗？此操作无法撤销。`)) {
        const chatIndex = allChats.findIndex(c => c.id === chatId);
        allChats.splice(chatIndex, 1);

        // 如果删除的是当前对话，需要切换到其他对话
        if (chatId === currentChatId) {
            const newIndexToActivate = Math.max(0, chatIndex - 1);
            const newChatIdToActivate = allChats[newIndexToActivate].id;
            switchChat(newChatIdToActivate);
        } else {
            // 如果删除的不是当前对话，只需要重新渲染侧边栏和保存数据
            renderSidebar();
            saveDataToStorage();
        }
    }
}

// --- UI Rendering ---
function renderSidebar() {
    elements.chatList.innerHTML = '';
    allChats.forEach(chat => {
        const li = document.createElement('li');
        li.className = 'chat-item';
        li.dataset.chatId = chat.id;
        if (chat.id === currentChatId) li.classList.add('active-chat');

        // 创建对话标题容器
        const titleSpan = document.createElement('span');
        titleSpan.className = 'chat-title';
        titleSpan.textContent = chat.title;
        titleSpan.addEventListener('click', () => switchChat(chat.id));

        // 创建删除按钮
        const deleteButton = document.createElement('button');
        deleteButton.className = 'delete-chat-btn';
        deleteButton.innerHTML = ICONS.delete;
        deleteButton.title = '删除对话';
        deleteButton.addEventListener('click', (e) => {
            e.stopPropagation(); // 防止触发切换对话
            deleteChat(chat.id);
        });

        li.appendChild(titleSpan);
        li.appendChild(deleteButton);
        elements.chatList.appendChild(li);
    });
}

function renderChatContent() {
    const chat = getCurrentChat();
    if (!chat) return;

    elements.chatViewContainer.style.display = 'flex';
    elements.chatTitle.textContent = chat.title;
    elements.chatbox.innerHTML = ''; // Clear previous content

    if (chat.history.length === 0) {
        elements.chatbox.appendChild(elements.welcomeScreen);
        elements.welcomeScreen.style.display = 'flex';
    } else {
        elements.welcomeScreen.style.display = 'none';
        chat.history.forEach(msg => addMessageToChatbox(msg.role === 'model' ? 'ai' : 'user', msg.parts[0].text));
    }
}

function switchChat(chatId) {
    if (chatId === currentChatId) return;
    currentChatId = chatId;
    renderChatContent();
    renderSidebar();
    saveDataToStorage();

    // 移动端切换对话后自动关闭侧边栏
    if (window.innerWidth <= 768) {
        closeMobileSidebar();
    }
}

function addMessageToChatbox(sender, text, images = []) {
    elements.welcomeScreen.style.display = 'none';
    const wrapper = document.createElement('div');
    wrapper.className = `message-wrapper ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    if (sender === 'ai') avatar.innerHTML = ICONS.aiAvatar;
    else avatar.innerHTML = 'U';

    const messageElement = document.createElement('div');
    messageElement.classList.add('message', `${sender}-message`);

    // 添加文本内容
    if (text && text.trim()) {
        const textContent = sender === 'ai' ? marked.parse(text) : text.replace(/</g, "&lt;").replace(/>/g, "&gt;");
        messageElement.innerHTML = textContent;
    } else if (!images || images.length === 0) {
        // 如果既没有文本也没有图片，显示占位符
        messageElement.innerHTML = '<em>空消息</em>';
    }

    // 添加图片内容
    if (images && images.length > 0) {
        const imagesContainer = document.createElement('div');
        imagesContainer.className = 'message-images';

        images.forEach(imageData => {
            const img = document.createElement('img');
            img.src = imageData.dataUrl;
            img.alt = imageData.name;
            img.className = 'message-image';
            img.title = `${imageData.name} (${formatFileSize(imageData.size)})`;

            // 点击图片放大查看
            img.onclick = () => showImageModal(imageData.dataUrl, imageData.name);

            imagesContainer.appendChild(img);
        });

        messageElement.appendChild(imagesContainer);
    }

    wrapper.appendChild(avatar);
    wrapper.appendChild(messageElement);
    elements.chatbox.appendChild(wrapper);
    elements.chatbox.scrollTop = elements.chatbox.scrollHeight;
}

// --- Core Chat Logic ---
async function sendMessage() {
    const userText = elements.userInput.value.trim();
    const hasImages = selectedImages.length > 0;

    // 检查是否有内容发送（文本或图片）
    if ((!userText && !hasImages) || elements.sendButton.disabled) return;

    const currentChat = getCurrentChat();
    if (!currentChat) return;

    if (currentChat.history.length === 0) {
        let title;
        if (userText) {
            title = userText;
        } else if (hasImages) {
            title = selectedImages.length === 1 ? '图片对话' : `${selectedImages.length}张图片对话`;
        } else {
            title = '新对话';
        }
        currentChat.title = title.substring(0, 20) + (title.length > 20 ? '...' : '');
        renderSidebar();
        elements.chatTitle.textContent = currentChat.title;
    }

    // 构建消息内容
    const messageParts = [];
    if (userText) {
        messageParts.push({ text: userText });
    }

    // 添加图片到消息中
    const messageImages = [];
    if (hasImages) {
        selectedImages.forEach(imageData => {
            messageParts.push({
                inline_data: {
                    mime_type: imageData.file.type,
                    data: convertImageToBase64(imageData)
                }
            });
            messageImages.push(imageData);
        });
    }

    // 显示用户消息（包含图片）
    addMessageToChatbox('user', userText, messageImages);

    // 清空输入
    elements.userInput.value = '';
    elements.userInput.style.height = 'auto';
    clearAllImages();

    // 保存到历史记录
    currentChat.history.push({ role: 'user', parts: messageParts });
    saveDataToStorage();

    toggleInput(true);
    const thinkingElement = showThinkingIndicator();

    try {
        const response = await fetch(getApiUrl(), {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ contents: currentChat.history }),
        });

        thinkingElement.remove();
        const data = await response.json();

        if (response.ok && data.candidates && data.candidates.length > 0 && data.candidates[0].content) {
            const aiText = data.candidates[0].content.parts[0].text;
            currentChat.history.push({ role: 'model', parts: [{ text: aiText }] });
            addMessageToChatbox('ai', aiText);
        } else {
            const errorMsg = data.error?.message || '模型未返回有效回复。';
            throw new Error(errorMsg);
        }
    } catch (error) {
        console.error('Error calling API:', error);
        thinkingElement?.remove();
        addMessageToChatbox('error', `请求失败: ${error.message}`);
        currentChat.history.pop();
    } finally {
        saveDataToStorage();
        toggleInput(false);
    }
}

// --- Mobile Menu Functions ---
function toggleMobileSidebar() {
    const isOpen = elements.sidebar.classList.contains('mobile-open');
    if (isOpen) {
        closeMobileSidebar();
    } else {
        openMobileSidebar();
    }
}

function openMobileSidebar() {
    elements.sidebar.classList.add('mobile-open');
    elements.mobileOverlay.classList.add('active');
    document.body.style.overflow = 'hidden'; // 防止背景滚动
}

function closeMobileSidebar() {
    elements.sidebar.classList.remove('mobile-open');
    elements.mobileOverlay.classList.remove('active');
    document.body.style.overflow = ''; // 恢复滚动
}

// 检测屏幕尺寸变化，自动关闭移动端菜单
function handleResize() {
    if (window.innerWidth > 768) {
        closeMobileSidebar();
    }
}

window.addEventListener('resize', handleResize);

// --- Custom Select Functions ---
function toggleCustomSelect() {
    const isOpen = elements.selectOptions.classList.contains('show');
    if (isOpen) {
        closeCustomSelect();
    } else {
        openCustomSelect();
    }
}

function openCustomSelect() {
    elements.selectTrigger.classList.add('active');
    elements.selectOptions.classList.add('show');
}

function closeCustomSelect() {
    elements.selectTrigger.classList.remove('active');
    elements.selectOptions.classList.remove('show');
}

function handleSelectOption(e) {
    const option = e.target.closest('.select-option');
    if (!option) return;

    const value = option.dataset.value;
    const name = option.querySelector('.option-name').textContent;

    // 更新显示值
    elements.selectValue.textContent = name;

    // 更新选中状态
    elements.selectOptions.querySelectorAll('.select-option').forEach(opt => {
        opt.classList.remove('selected');
    });
    option.classList.add('selected');

    // 关闭选择器
    closeCustomSelect();

    // 触发提供商变更
    onSettingsProviderChange(value);
}

// --- Settings Functions ---
function openSettings() {
    // 设置当前选择的提供商到自定义选择器
    if (elements.selectValue) {
        elements.selectValue.textContent = getProviderDisplayName(selectedProvider);

        // 更新选中状态
        elements.selectOptions.querySelectorAll('.select-option').forEach(opt => {
            opt.classList.remove('selected');
            if (opt.dataset.value === selectedProvider) {
                opt.classList.add('selected');
            }
        });
    }

    // 更新设置界面
    updateSettingsInterface();

    elements.settingsOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeSettings() {
    elements.settingsOverlay.classList.remove('active');
    document.body.style.overflow = '';
}

// 更新设置界面
function updateSettingsInterface() {
    // 从自定义选择器或全局变量获取当前提供商
    const currentProvider = selectedProvider;
    const providerDisplayName = getProviderDisplayName(currentProvider);

    // 填充当前选择的提供商的API Key
    const currentProviderApiKey = apiKeys[currentProvider] || '';
    if (elements.settingsApiKeyInput) {
        elements.settingsApiKeyInput.value = currentProviderApiKey;
    }

    // 更新API Key获取链接
    if (elements.settingsProviderLink) {
        const providerUrls = {
            'DeepSeek': 'https://platform.deepseek.com/api_keys',
            'Gemini': 'https://ai.google.dev/gemini-api/explorer/api_key',
            'OpenRouter': 'https://openrouter.ai/keys',
            'Custom': '#'
        };
        elements.settingsProviderLink.href = providerUrls[currentProvider] || '#';

        // 自定义模式下隐藏获取链接
        if (currentProvider === 'Custom') {
            elements.settingsProviderLink.style.display = 'none';
        } else {
            elements.settingsProviderLink.style.display = 'inline';
        }
    }

    // 更新API地址相关
    updateApiUrlInterface(currentProvider, providerDisplayName);
}

// 更新API地址界面
function updateApiUrlInterface(provider, providerDisplayName) {
    if (!elements.settingsApiUrlInput || !elements.apiUrlHint || !elements.apiUrlGroup) return;

    const providerDefaultUrl = PROVIDER_API_URLS[provider];

    if (provider === 'Custom') {
        // 自定义模式：API地址为必填
        elements.settingsApiUrlInput.placeholder = '请输入自定义API地址';
        elements.settingsApiUrlInput.required = true;
        elements.apiUrlHint.textContent = '自定义模式下必须输入API地址';
        elements.apiUrlGroup.classList.add('custom-mode');

        // 自定义模式下显示当前保存的API地址（如果有的话）
        elements.settingsApiUrlInput.value = apiUrl || '';
    } else {
        // 标准模式：显示提供商的默认API地址
        elements.settingsApiUrlInput.placeholder = `默认: ${providerDefaultUrl}`;
        elements.settingsApiUrlInput.required = false;
        elements.apiUrlHint.textContent = `当前使用 ${providerDisplayName} 默认API地址`;
        elements.apiUrlGroup.classList.remove('custom-mode');

        // 显示当前提供商的默认API地址
        elements.settingsApiUrlInput.value = providerDefaultUrl || '';
    }
}

// 设置模态框中提供商选择变化处理
function onSettingsProviderChange(newProvider) {
    // 如果没有传入参数，从选择器获取（向后兼容）
    if (!newProvider && elements.settingsProviderSelect) {
        newProvider = elements.settingsProviderSelect.value;
    }

    if (!newProvider) return;

    // 更新全局选择的提供商
    selectedProvider = newProvider;

    // 自动填写对应提供商的默认API地址（除了自定义模式）
    if (newProvider !== 'Custom' && elements.settingsApiUrlInput) {
        const defaultUrl = PROVIDER_API_URLS[newProvider];
        if (defaultUrl) {
            elements.settingsApiUrlInput.value = defaultUrl;
            // 同时更新全局API URL
            apiUrl = defaultUrl;
        }
    }

    // 更新界面
    updateSettingsInterface();

    // 显示提示信息
    const providerDisplayName = getProviderDisplayName(newProvider);
    showToast(`已切换到 ${providerDisplayName}`, 'info', 2000);
}

// 设置模态框中的API Key可见性切换
function toggleSettingsApiKeyVisibility() {
    if (!elements.settingsToggleApiKeyVisibilityBtn || !elements.settingsApiKeyInput) return;

    if (elements.settingsApiKeyInput.type === 'password') {
        elements.settingsApiKeyInput.type = 'text';
        if (elements.settingsEyeOpenIcon) elements.settingsEyeOpenIcon.style.display = 'none';
        if (elements.settingsEyeClosedIcon) elements.settingsEyeClosedIcon.style.display = 'block';
        elements.settingsToggleApiKeyVisibilityBtn.setAttribute('aria-label', '隐藏 API Key');
    } else {
        elements.settingsApiKeyInput.type = 'password';
        if (elements.settingsEyeOpenIcon) elements.settingsEyeOpenIcon.style.display = 'block';
        if (elements.settingsEyeClosedIcon) elements.settingsEyeClosedIcon.style.display = 'none';
        elements.settingsToggleApiKeyVisibilityBtn.setAttribute('aria-label', '显示 API Key');
    }
}

function saveSettings() {
    const newApiKey = elements.settingsApiKeyInput.value.trim();
    const newApiUrl = elements.settingsApiUrlInput.value.trim();
    const settingsProvider = selectedProvider; // 使用全局的selectedProvider

    if (!newApiKey) {
        showToast('请输入有效的 API Key！', 'error', 3000);
        return;
    }

    // 自定义模式下验证API地址
    if (settingsProvider === 'Custom' && !newApiUrl) {
        showToast('自定义模式下必须输入API地址！', 'error', 3000);
        elements.settingsApiUrlInput.focus();
        return;
    }

    // 保存到选择的提供商
    apiKeys[selectedProvider] = newApiKey;
    localStorage.setItem('aiInsightApiKeys', JSON.stringify(apiKeys));

    // 保存API Key到全局变量（保持向后兼容）
    apiKey = newApiKey;
    saveApiKeyToStorage(newApiKey);

    // 保存API URL
    if (settingsProvider === 'Custom') {
        // 自定义模式：使用用户输入的API地址
        apiUrl = newApiUrl;
    } else {
        // 标准模式：使用提供商的默认API地址
        apiUrl = PROVIDER_API_URLS[settingsProvider] || DEFAULT_API_URL;
    }
    saveApiUrlToStorage(apiUrl);

    // 更新初始页面的API Key输入框和提供商选择
    if (elements.apiKeyInput) {
        elements.apiKeyInput.value = newApiKey;
    }

    // 更新初始页面的提供商选择
    const initialProviderRadio = document.querySelector(`input[name="model_provider"][value="${selectedProvider}"]`);
    if (initialProviderRadio) {
        initialProviderRadio.checked = true;
        updateApiKeySection(); // 更新初始页面的界面
    }

    closeSettings();
    showToast(`${getProviderDisplayName(selectedProvider)} 设置已保存！`, 'success');
}

function resetSettings() {
    if (confirm('确定要重置所有设置为默认值吗？')) {
        // 重置为默认值
        apiUrl = DEFAULT_API_URL;

        // 清除存储的API URL
        try {
            localStorage.removeItem(API_URL_STORAGE_KEY);
        } catch (e) {
            console.error("清除API URL失败:", e);
        }

        // 更新输入框
        elements.settingsApiUrlInput.value = '';

        alert('设置已重置为默认值！');
    }
}

function clearAllData() {
    const confirmText = '删除全部数据';
    const userInput = prompt(
        `此操作将永久删除所有本地数据，包括：\n• 所有对话历史\n• API Key 和设置\n• 其他应用数据\n\n此操作无法撤销！\n\n如果确定要继续，请在下方输入"${confirmText}"：`
    );

    if (userInput === confirmText) {
        try {
            // 清除所有本地存储数据
            localStorage.removeItem(STORAGE_KEY);
            localStorage.removeItem(API_KEY_STORAGE_KEY);
            localStorage.removeItem(API_URL_STORAGE_KEY);

            // 重置应用状态
            apiKey = '';
            apiUrl = DEFAULT_API_URL;
            allChats = [];
            currentChatId = null;

            // 关闭设置模态框
            closeSettings();

            // 返回到API Key输入页面
            elements.appContainer.style.display = 'none';
            elements.apiKeyView.style.display = 'flex';
            elements.apiKeyInput.value = '';

            alert('所有本地数据已清除！');
        } catch (e) {
            console.error("清除数据失败:", e);
            alert('清除数据时发生错误，请刷新页面后重试。');
        }
    } else if (userInput !== null) {
        alert('输入不匹配，操作已取消。');
    }
}

// --- Helpers ---
function getCurrentChat() { return allChats.find(c => c.id === currentChatId); }
function showThinkingIndicator() {
    const el = document.createElement('div');
    el.classList.add('thinking');
    el.textContent = 'AI 正在思考中...';
    elements.chatbox.appendChild(el);
    elements.chatbox.scrollTop = elements.chatbox.scrollHeight;
    return el;
}
function toggleInput(disabled) {
    elements.userInput.disabled = disabled;
    elements.sendButton.disabled = disabled;
    if (!disabled) elements.userInput.focus();
}

// --- 新的API Key界面功能 ---

// 全局状态管理
let apiKeys = JSON.parse(localStorage.getItem('aiInsightApiKeys')) || {
    DeepSeek: '',
    Gemini: '',
    OpenRouter: '',
    Custom: ''
};
let selectedProvider = 'DeepSeek';

// 初始化API Key界面
function initializeApiKeyInterface() {
    if (elements.modelProviderRadios.length > 0) {
        selectedProvider = document.querySelector('input[name="model_provider"]:checked')?.value || 'DeepSeek';
        updateApiKeySection();
        updateStartAppButtonState();
    }
}

// API Key可见性切换
function toggleApiKeyVisibility() {
    if (!elements.toggleApiKeyVisibilityBtn || !elements.apiKeyInput) return;

    if (elements.apiKeyInput.type === 'password') {
        elements.apiKeyInput.type = 'text';
        if (elements.eyeOpenIcon) elements.eyeOpenIcon.style.display = 'none';
        if (elements.eyeClosedIcon) elements.eyeClosedIcon.style.display = 'block';
        elements.toggleApiKeyVisibilityBtn.setAttribute('aria-label', '隐藏 API Key');
    } else {
        elements.apiKeyInput.type = 'password';
        if (elements.eyeOpenIcon) elements.eyeOpenIcon.style.display = 'block';
        if (elements.eyeClosedIcon) elements.eyeClosedIcon.style.display = 'none';
        elements.toggleApiKeyVisibilityBtn.setAttribute('aria-label', '显示 API Key');
    }
}

// 更新API Key部分
function updateApiKeySection() {
    const checkedRadio = document.querySelector('input[name="model_provider"]:checked');
    if (!checkedRadio) return;

    selectedProvider = checkedRadio.value;
    const url = checkedRadio.dataset.apikeyUrl;

    // 更新API Key输入框
    elements.apiKeyInput.value = apiKeys[selectedProvider] || '';
    elements.apiKeyInput.placeholder = `在此输入您的${getProviderDisplayName(selectedProvider)} API Key`;

    // 更新帮助链接
    if (elements.apiKeyHelpLink) {
        if (selectedProvider === 'Custom') {
            elements.apiKeyHelpLink.style.display = 'none';
        } else {
            elements.apiKeyHelpLink.style.display = 'inline-flex';
            elements.apiKeyHelpLink.href = url || '#';
            elements.apiKeyHelpLink.textContent = `如何获取${getProviderDisplayName(selectedProvider)} API Key？`;
        }
    }

    // 显示/隐藏自定义API地址输入框，并更新API地址
    if (elements.customApiUrlGroup) {
        if (selectedProvider === 'Custom') {
            elements.customApiUrlGroup.style.display = 'block';
            // 自定义模式下，显示当前保存的API地址
            if (elements.customApiUrlInput) {
                elements.customApiUrlInput.value = apiUrl || '';
            }
        } else {
            elements.customApiUrlGroup.style.display = 'none';
            // 非自定义模式下，自动设置为对应提供商的默认API地址
            const defaultUrl = PROVIDER_API_URLS[selectedProvider];
            if (defaultUrl) {
                apiUrl = defaultUrl;
                saveApiUrlToStorage(apiUrl);
            }
        }
    }

    updateStartAppButtonState();
}

// 获取供应商显示名称
function getProviderDisplayName(provider) {
    switch(provider) {
        case 'DeepSeek': return 'DeepSeek';
        case 'Gemini': return 'Gemini';
        case 'OpenRouter': return 'OpenRouter';
        case 'Custom': return '自定义';
        default: return provider;
    }
}

// 更新开始应用按钮状态
function updateStartAppButtonState() {
    const currentApiKey = elements.apiKeyInput.value.trim();
    let isDisabled = !currentApiKey;

    // 自定义模式下还需要验证API地址
    if (selectedProvider === 'Custom' && elements.customApiUrlInput) {
        const customApiUrl = elements.customApiUrlInput.value.trim();
        isDisabled = isDisabled || !customApiUrl;
    }

    if (elements.submitApiKeyButton) {
        elements.submitApiKeyButton.disabled = isDisabled;
    }
}

// 保存API Key
function saveApiKey() {
    apiKeys[selectedProvider] = elements.apiKeyInput.value.trim();
    localStorage.setItem('aiInsightApiKeys', JSON.stringify(apiKeys));
    updateStartAppButtonState();
}

// 测试连接
function testConnection() {
    const apiKeyValue = elements.apiKeyInput.value.trim();
    if (!apiKeyValue) {
        showToast('请输入API Key后再测试。', 'info', 2000);
        return;
    }

    showToast(`正在测试 ${getProviderDisplayName(selectedProvider)} 连接...`);

    // 模拟网络请求
    setTimeout(() => {
        const isSuccess = Math.random() > 0.2; // 模拟80%成功率
        if (isSuccess) {
            saveApiKey();
            showToast(`🚀 ${getProviderDisplayName(selectedProvider)} 连接成功！API Key 已保存。`, 'success');
        } else {
            showToast(`❌ ${getProviderDisplayName(selectedProvider)} 连接失败，请检查 API Key 或网络。`, 'error');
        }
    }, 1500);
}

// 显示Toast通知
function showToast(message, type = 'info', duration = 3000) {
    // 创建toast元素如果不存在
    let toast = document.getElementById('toast-notification');
    if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast-notification';
        toast.className = 'toast-notification';
        toast.setAttribute('role', 'status');
        toast.setAttribute('aria-live', 'polite');
        document.body.appendChild(toast);
    }

    toast.textContent = message;

    // 设置样式
    if (type === 'success') {
        toast.style.backgroundColor = 'var(--success-color)';
    } else if (type === 'error') {
        toast.style.backgroundColor = 'var(--warning-color)';
    } else {
        toast.style.backgroundColor = 'rgba(51, 51, 51, 0.9)';
    }

    toast.classList.add('show');

    // 清除之前的定时器
    clearTimeout(toast.dataset.timeoutId);

    const timeoutId = setTimeout(() => {
        toast.classList.remove('show');
    }, duration);
    toast.dataset.timeoutId = timeoutId;
}

// --- 图片处理函数 ---
function handleImageUpload(event) {
    const files = Array.from(event.target.files);

    files.forEach(file => {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imageData = {
                    file: file,
                    dataUrl: e.target.result,
                    name: file.name,
                    size: file.size
                };
                selectedImages.push(imageData);
                updateImagePreview();
            };
            reader.readAsDataURL(file);
        }
    });

    // 清空input以允许重复选择同一文件
    event.target.value = '';
}

function updateImagePreview() {
    if (selectedImages.length === 0) {
        elements.imagePreviewContainer.style.display = 'none';
        return;
    }

    elements.imagePreviewContainer.style.display = 'block';
    elements.imagePreviewList.innerHTML = '';

    selectedImages.forEach((imageData, index) => {
        const previewItem = document.createElement('div');
        previewItem.className = 'image-preview-item';

        const img = document.createElement('img');
        img.src = imageData.dataUrl;
        img.alt = imageData.name;

        const removeBtn = document.createElement('button');
        removeBtn.className = 'image-preview-remove';
        removeBtn.innerHTML = '×';
        removeBtn.title = '移除图片';
        removeBtn.onclick = () => removeImage(index);

        previewItem.appendChild(img);
        previewItem.appendChild(removeBtn);
        elements.imagePreviewList.appendChild(previewItem);
    });
}

function removeImage(index) {
    selectedImages.splice(index, 1);
    updateImagePreview();
}

function clearAllImages() {
    selectedImages = [];
    updateImagePreview();
}

// 将图片转换为base64格式用于API调用
function convertImageToBase64(imageData) {
    // 移除data URL的前缀，只保留base64数据
    return imageData.dataUrl.split(',')[1];
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 显示图片模态框
function showImageModal(imageSrc, imageName) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'image-modal-overlay';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = imageSrc;
    img.alt = imageName;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 8px;
        cursor: default;
    `;

    // 点击图片不关闭模态框
    img.onclick = (e) => e.stopPropagation();

    // 点击模态框背景关闭
    modal.onclick = () => {
        document.body.removeChild(modal);
        document.body.style.overflow = '';
    };

    // ESC键关闭
    const handleEsc = (e) => {
        if (e.key === 'Escape') {
            document.body.removeChild(modal);
            document.body.style.overflow = '';
            document.removeEventListener('keydown', handleEsc);
        }
    };
    document.addEventListener('keydown', handleEsc);

    modal.appendChild(img);
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}
