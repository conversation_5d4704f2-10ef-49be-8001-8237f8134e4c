<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 对话应用</title>
    
    <!-- 引入 Google 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="style.css">
    
    <!-- 引入 Marked.js 库用于渲染 Markdown -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>

    <!-- 主应用容器，默认隐藏 -->
    <div id="app-container" style="display:none;">
        <!-- 移动端遮罩层 -->
        <div class="mobile-overlay" id="mobile-overlay"></div>

        <div id="sidebar">
            <button id="new-chat-button"></button>
            <ul id="chat-list"></ul>
            <button id="settings-button" class="settings-btn" title="设置">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .***********.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
                </svg>
                <span>设置</span>
            </button>
        </div>
        <div id="chat-view-container">
            <header>
                <button id="mobile-menu-button" class="mobile-only">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                    </svg>
                </button>
                <span id="chat-title">对话</span>
                <div class="header-spacer mobile-only"></div>
            </header>
            <div id="chatbox">
                <!-- 新对话的欢迎界面 -->
                <div id="welcome-screen">
                    <div class="welcome-logo"></div>
                    <h2>你好，我是你的AI助手</h2>
                    <p>我可以回答问题、提供灵感、总结文本等等</p>
                    <div class="prompt-examples">
                        <div class="prompt-example">写一首关于宇宙的诗</div>
                        <div class="prompt-example">用简单的语言解释什么是黑洞</div>
                        <div class="prompt-example">给我推荐三部科幻电影</div>
                    </div>
                </div>
            </div>
            <div id="input-area">
                <div id="input-wrapper">
                    <button id="image-upload-button" title="上传图片" class="image-upload-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                        </svg>
                    </button>
                    <input type="file" id="image-input" accept="image/*" style="display: none;" multiple>
                    <textarea id="user-input" placeholder="输入消息..." rows="1"></textarea>
                    <button id="send-button" title="发送"></button>
                </div>
                <div id="image-preview-container" style="display: none;">
                    <div id="image-preview-list"></div>
                    <button id="clear-images-button" class="clear-images-btn" title="清除所有图片">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- API Key 输入视图 -->
    <div id="api-key-view">
        <div class="main-setup-container">
            <!-- 头部区域：应用名称与欢迎语 -->
            <div class="main-setup-header">
                <span class="logo">✨</span>
                <h1>欢迎使用 AI 对话应用</h1>
                <p>在这里，您可以配置您的语言模型服务提供商，轻松开启与AI的智能对话之旅。</p>
            </div>

            <!-- 模型供应商选择区 -->
            <div class="setup-section">
                <h2>选择您的模型供应商</h2>
                <div class="radio-group" role="radiogroup" aria-label="选择您的AI模型供应商">
                    <label>
                        <input type="radio" name="model_provider" value="DeepSeek" checked data-apikey-url="https://platform.deepseek.com/api_keys">
                        <span>DeepSeek</span>
                    </label>
                    <label>
                        <input type="radio" name="model_provider" value="Gemini" data-apikey-url="https://aistudio.google.com/apikey">
                        <span>Gemini</span>
                    </label>
                    <label>
                        <input type="radio" name="model_provider" value="OpenRouter" data-apikey-url="https://openrouter.ai/keys">
                        <span>OpenRouter</span>
                    </label>
                    <label>
                        <input type="radio" name="model_provider" value="Custom" data-apikey-url="#">
                        <span>自定义</span>
                    </label>
                </div>
            </div>

            <!-- API Key 输入与管理区 -->
            <div class="setup-section">
                <h2>您的API Key</h2>
                <div class="api-key-input-group">
                    <input type="password" id="api-key-input" placeholder="在此输入您的API Key" autocomplete="off" aria-label="API Key输入框">
                    <button class="toggle-visibility-btn" id="toggle-api-key-visibility" aria-label="Toggle API Key visibility">
                        <!-- Eye open icon SVG -->
                        <svg id="eye-open-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: block;">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zm0 13c-3.03 0-5.5-2.47-5.5-5.5s2.47-5.5 5.5-5.5 5.5 2.47 5.5 5.5-2.47 5.5-5.5 5.5zm0-8.5c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5 3.5-1.57 3.5-3.5-1.57-3.5-3.5-3.5z" fill="currentColor"/>
                        </svg>
                        <!-- Eye closed icon SVG -->
                        <svg id="eye-closed-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                            <path opacity="0.4" d="M12 7c2.76 0 5 2.24 5 5 0 .34-.04.67-.09 1H22c-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7L8.03 8.03A5.954 5.954 0 0 1 12 7z" fill="currentColor"/>
                            <path d="M2 4.27l2.28 2.28.46.46C3.06 7.91 1 12 1 12s4.27 7.5 11 7.5c1.09 0 2.16-.18 3.17-.5L17.73 20l1.27 1.27 1.4-1.4L3.4 2.87 2 4.27zm10 13c-3.03 0-5.5-2.47-5.5-5.5 0-.41.06-.82.16-1.2l1.76 1.76c-.03.18-.08.38-.08.64 0 1.93 1.57 3.5 3.5 3.5.26 0 .47-.05.64-.08l1.76 1.76c-.38.1-.79.16-1.2.16zm2.25-4.47l-2.02-2.02c.06-.04.12-.07.18-.09.64-.19 1.25-.09 1.72.38.47.47.56 1.08.38 1.72-.02.06-.05.12-.09.18z" fill="currentColor"/>
                        </svg>
                    </button>
                </div>

                <!-- 自定义API地址输入框 -->
                <div class="custom-api-url-group" id="custom-api-url-group" style="display: none;">
                    <input type="text" id="custom-api-url-input" placeholder="请输入自定义API地址" aria-label="自定义API地址输入框">
                    <small class="custom-api-hint">例如: https://api.example.com/v1/chat/completions</small>
                </div>

                <div class="api-key-info">
                    <a href="#" id="api-key-help-link" class="api-key-link" target="_blank" rel="noopener noreferrer">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M10 6H6C4.89543 6 4 6.89543 4 8V18C4 19.1046 4.89543 20 6 20H16C17.1046 20 18 19.1046 18 18V14"></path>
                            <path d="M14 4L20 4V10"></path>
                            <path d="M20 4L11 13"></path>
                        </svg>
                        如何获取Google AI API Key？
                    </a>
                    <p class="api-key-warning" id="api-key-warning">请妥善保管您的API Key，切勿与他人分享！</p>
                </div>
                <div class="control-buttons-group">
                    <button id="test-connection-btn" class="test-connection-btn">测试连接</button>
                    <button id="submit-api-key-button" class="start-app-btn" disabled>保存并进入应用</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div id="settings-overlay" class="modal-overlay">
        <div id="settings-modal" class="modal">
            <div class="modal-header">
                <h3>设置</h3>
                <button id="close-settings-button" class="close-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-content">
                <div class="setting-group">
                    <label>API提供商</label>
                    <div class="custom-select" id="custom-provider-select">
                        <div class="select-trigger" id="select-trigger">
                            <span class="select-value" id="select-value">DeepSeek</span>
                            <svg class="select-arrow" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="6,9 12,15 18,9"></polyline>
                            </svg>
                        </div>
                        <div class="select-options" id="select-options">
                            <div class="select-option" data-value="DeepSeek">
                                <div class="option-content">
                                    <span class="option-name">DeepSeek</span>
                                    <span class="option-desc">中国领先的AI公司</span>
                                </div>
                            </div>
                            <div class="select-option" data-value="Gemini">
                                <div class="option-content">
                                    <span class="option-name">Gemini</span>
                                    <span class="option-desc">Google AI模型</span>
                                </div>
                            </div>
                            <div class="select-option" data-value="OpenRouter">
                                <div class="option-content">
                                    <span class="option-name">OpenRouter</span>
                                    <span class="option-desc">多模型API聚合</span>
                                </div>
                            </div>
                            <div class="select-option" data-value="Custom">
                                <div class="option-content">
                                    <span class="option-name">自定义</span>
                                    <span class="option-desc">自定义API端点</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="provider-help">
                        <a id="settings-provider-link" href="https://platform.deepseek.com/api_keys" target="_blank" class="provider-link">
                            获取API Key
                        </a>
                    </div>
                </div>
                <div class="setting-group" id="api-url-group">
                    <label for="settings-api-url-input">API 地址</label>
                    <input type="text" id="settings-api-url-input" placeholder="留空使用默认地址">
                    <small class="setting-hint" id="api-url-hint">留空使用默认 DeepSeek API 地址</small>
                </div>
                <div class="setting-group">
                    <label for="settings-api-key-input">API Key</label>
                    <div class="api-key-input-wrapper">
                        <input type="password" id="settings-api-key-input" placeholder="输入您的 API Key">
                        <button type="button" id="settings-toggle-api-key-visibility" class="toggle-visibility-btn" aria-label="显示 API Key">
                            <svg id="settings-eye-open-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                            </svg>
                            <svg id="settings-eye-closed-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                <path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="modal-actions">
                    <button id="save-settings-button" class="primary-btn">保存设置</button>
                    <button id="reset-settings-button" class="secondary-btn">重置为默认</button>
                </div>
                <div class="danger-zone">
                    <h4>危险操作</h4>
                    <p>以下操作将永久删除所有本地数据，包括对话历史和设置，且无法恢复。</p>
                    <button id="clear-all-data-button" class="danger-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                        </svg>
                        删除全部本地数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入逻辑脚本文件，defer 属性确保在 HTML 解析完毕后执行 -->
    <script src="script.js" defer></script>
</body>
</html>
