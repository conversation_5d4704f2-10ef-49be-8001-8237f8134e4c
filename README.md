# AI 对话应用 - 图片功能

这是一个支持图片上传的AI对话应用，用户可以在对话中发送图片并获得AI的分析和回复。

## 新增功能

### 图片上传功能
- **图片上传按钮**: 在输入框左侧添加了图片上传按钮（📷图标）
- **多图片支持**: 支持一次选择和上传多张图片
- **图片预览**: 上传后可以在输入区域预览选中的图片
- **图片管理**: 可以单独删除某张图片或清除所有图片

### 图片显示功能
- **消息中的图片**: 发送的图片会在对话中正确显示
- **图片点击放大**: 点击消息中的图片可以放大查看
- **文件信息**: 鼠标悬停在图片上可以看到文件名和大小

### 支持的图片格式
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)
- 其他浏览器支持的图片格式

## 使用方法

1. **上传图片**:
   - 点击输入框左侧的图片按钮（📷）
   - 选择一张或多张图片
   - 图片会显示在输入区域的预览中

2. **发送消息**:
   - 可以只发送图片（不输入文字）
   - 可以同时发送图片和文字
   - 点击发送按钮或按Enter键发送

3. **管理图片**:
   - 点击预览图片右上角的 × 按钮删除单张图片
   - 点击预览区域右上角的 × 按钮清除所有图片

4. **查看图片**:
   - 在对话中点击图片可以放大查看
   - 点击模态框外部或按ESC键关闭放大视图

## 技术实现

### 前端功能
- 使用HTML5 File API处理图片上传
- 使用FileReader API将图片转换为Base64格式
- 实现图片预览和管理界面
- 添加图片放大查看功能

### API集成
- 支持Gemini API的多模态输入格式
- 将图片转换为API所需的inline_data格式
- 保持与现有API提供商的兼容性

### 样式优化
- 响应式设计，支持移动端
- 图片预览的缩略图显示
- 优雅的图片放大查看体验
- 与现有UI风格保持一致

## 文件结构

```
├── index.html          # 主页面，包含图片上传相关HTML
├── script.js           # 主要逻辑，包含图片处理函数
├── style.css           # 样式文件，包含图片相关样式
├── test-image.html     # 图片功能测试页面
└── README.md           # 本文档
```

## 主要新增函数

### JavaScript函数
- `handleImageUpload()`: 处理图片上传
- `updateImagePreview()`: 更新图片预览
- `removeImage()`: 删除单张图片
- `clearAllImages()`: 清除所有图片
- `convertImageToBase64()`: 转换图片为Base64
- `formatFileSize()`: 格式化文件大小
- `showImageModal()`: 显示图片放大视图

### CSS类
- `.image-upload-btn`: 图片上传按钮样式
- `.image-preview-container`: 图片预览容器
- `.image-preview-item`: 单个图片预览项
- `.message-images`: 消息中的图片容器
- `.message-image`: 消息中的图片样式

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## UI优化 (最新更新)

### 输入框对齐修复
- **问题**: 发送按钮、输入框和图片上传按钮对齐不一致
- **解决**: 重新设计了输入区域的布局，使用flex布局确保所有元素完美对齐
- **改进**:
  - 发送按钮从绝对定位改为flex布局
  - 统一了所有按钮的尺寸和间距
  - 优化了移动端的显示效果

### 对话卡片浮动修复
- **问题**: 鼠标悬停在对话卡片上时，卡片会向右浮动导致显示不完全
- **解决**: 移除了hover状态下的`transform: translateX(2px)`效果
- **改进**: 保留了hover的背景色变化，但不再有位移动画

### AI头像图标修复
- **问题**: AI助手的头像图标显示为问号或不正确的图标
- **解决**: 更换为清晰的机器人图标，更好地代表AI助手身份
- **改进**: 新图标在不同尺寸下都保持清晰，与整体设计风格一致

### 移动端优化 (最新更新)
- **标题栏高度**: 移动端标题栏高度增加到64px，提供更好的视觉比例和触摸体验
- **按钮尺寸**: 汉堡菜单按钮最小尺寸44x44px，符合iOS/Android触摸标准
- **API Key页面**: 优化移动端布局，确保内容完整显示，支持垂直滚动
- **字体优化**: 调整移动端字体大小，提高可读性
- **图片功能**: 移动端图片预览容器优化，支持横向滚动

### 响应式优化
- **桌面端**: 按钮尺寸40px，间距12px
- **移动端**: 按钮尺寸36px，间距8px，标题栏64px高度
- **平板端**: 自适应布局，确保在各种屏幕尺寸下都有良好表现

## 注意事项

1. **文件大小限制**: 建议单张图片不超过10MB
2. **API支持**: 目前主要针对Gemini API优化，其他API提供商可能需要额外配置
3. **存储**: 图片不会永久存储，刷新页面后会丢失
4. **隐私**: 图片仅在本地处理，不会上传到除AI API外的其他服务器

## 测试文件

- `test-image.html`: 测试图片功能的基本组件
- `test-alignment.html`: 测试输入框对齐效果
- `test-chat-items.html`: 测试对话卡片hover效果
- `test-complete.html`: 完整的UI修复效果演示
- `test-mobile.html`: 移动端优化效果演示和对比
- `test-avatar.html`: AI头像图标修复效果演示
